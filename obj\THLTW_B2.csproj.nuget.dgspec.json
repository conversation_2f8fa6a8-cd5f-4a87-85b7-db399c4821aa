{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\THLTW_B2\\THLTW_B2\\THLTW_B2\\THLTW_B2.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\THLTW_B2\\THLTW_B2\\THLTW_B2\\THLTW_B2.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\THLTW_B2\\THLTW_B2\\THLTW_B2\\THLTW_B2.csproj", "projectName": "THLTW_B2", "projectPath": "C:\\Users\\<USER>\\Downloads\\THLTW_B2\\THLTW_B2\\THLTW_B2\\THLTW_B2.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\THLTW_B2\\THLTW_B2\\THLTW_B2\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.2, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[8.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}