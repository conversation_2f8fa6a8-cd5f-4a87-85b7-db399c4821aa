﻿@model THLTW_B2.Models.Product
@{
    ViewData["Title"] = "Xóa sản phẩm";
}

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h3><i class="fas fa-exclamation-triangle"></i> @ViewData["Title"]</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i>
                    <strong>Cảnh báo!</strong> Bạn có chắc chắn muốn xóa sản phẩm này không? Hành động này không thể hoàn tác.
                </div>

                <div class="row">
                    <div class="col-md-4">
                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                        {
                            <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded" style="max-height: 200px; object-fit: cover;" />
                        }
                        else
                        {
                            <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 200px;">
                                <i class="fas fa-image text-muted" style="font-size: 3rem;"></i>
                            </div>
                        }
                    </div>
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Tên sản phẩm:</strong></td>
                                <td>@Model.Name</td>
                            </tr>
                            <tr>
                                <td><strong>Giá:</strong></td>
                                <td><span class="text-success fw-bold">@Model.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span></td>
                            </tr>
                            <tr>
                                <td><strong>Danh mục:</strong></td>
                                <td>
                                    @if (Model.Category != null)
                                    {
                                        <span class="badge bg-primary">@Model.Category.Name</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa phân loại</span>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Mô tả:</strong></td>
                                <td>@Model.Description</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Hủy bỏ
                    </a>
                    <form asp-action="DeleteConfirmed" method="post" style="display: inline;">
                        <input type="hidden" asp-for="Id" />
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')">
                            <i class="fas fa-trash"></i> Xác nhận xóa
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>