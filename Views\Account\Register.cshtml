@model THLTW_B2.Models.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "Đăng ký";
}

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">@ViewData["Title"]</h3>
            </div>
            <div class="card-body">
                <form asp-action="Register" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post">
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="FullName" class="form-label"></label>
                        <input asp-for="FullName" class="form-control" placeholder="Nhập họ tên đầy đủ" />
                        <span asp-validation-for="FullName" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Email" class="form-label"></label>
                        <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="PhoneNumber" class="form-label"></label>
                        <input asp-for="PhoneNumber" class="form-control" placeholder="Nhập số điện thoại (tùy chọn)" />
                        <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Password" class="form-label"></label>
                        <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu (ít nhất 6 ký tự)" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="ConfirmPassword" class="form-label"></label>
                        <input asp-for="ConfirmPassword" class="form-control" placeholder="Nhập lại mật khẩu" />
                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">Đăng ký</button>
                    </div>
                </form>
                
                <div class="text-center mt-3">
                    <p>Đã có tài khoản? <a asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]">Đăng nhập ngay</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
