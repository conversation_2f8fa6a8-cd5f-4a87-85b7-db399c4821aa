﻿@model IEnumerable<THLTW_B2.Models.Product>
@{
    ViewData["Title"] = "Quản lý sản phẩm";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-box"></i> @ViewData["Title"]</h2>
    @if (User.IsInRole("Admin"))
    {
        <a asp-action="Add" class="btn btn-success">
            <i class="fas fa-plus"></i> Thêm sản phẩm mới
        </a>
    }
</div>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="card">
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Hình ảnh</th>
                            <th>Tên sản phẩm</th>
                            <th>Giá</th>
                            <th>Danh mục</th>
                            <th>Mô tả</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in Model)
                        {
                            <tr>
                                <td>
                                    @if (!string.IsNullOrEmpty(product.ImageUrl))
                                    {
                                        <img src="@product.ImageUrl" alt="@product.Name" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;" />
                                    }
                                    else
                                    {
                                        <div class="bg-light d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    }
                                </td>
                                <td>
                                    <strong>@product.Name</strong>
                                </td>
                                <td>
                                    <span class="text-success fw-bold">@product.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                </td>
                                <td>
                                    @if (product.Category != null)
                                    {
                                        <span class="badge bg-primary">@product.Category.Name</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa phân loại</span>
                                    }
                                </td>
                                <td>
                                    @if (product.Description.Length > 50)
                                    {
                                        <span title="@product.Description">@(product.Description.Substring(0, 50))...</span>
                                    }
                                    else
                                    {
                                        @product.Description
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Display" asp-route-id="@product.Id" class="btn btn-sm btn-info" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if (User.IsInRole("Admin"))
                                        {
                                            <a asp-action="Update" asp-route-id="@product.Id" class="btn btn-sm btn-warning" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@product.Id" class="btn btn-sm btn-danger" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-box-open text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">Chưa có sản phẩm nào</h4>
                @if (User.IsInRole("Admin"))
                {
                    <p class="text-muted">Hãy thêm sản phẩm đầu tiên của bạn!</p>
                    <a asp-action="Add" class="btn btn-success">
                        <i class="fas fa-plus"></i> Thêm sản phẩm mới
                    </a>
                }
            </div>
        }
    </div>
</div>
