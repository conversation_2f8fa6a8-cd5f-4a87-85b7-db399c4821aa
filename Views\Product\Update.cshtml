﻿@model THLTW_B2.Models.Product
@{
    ViewData["Title"] = "Chỉnh sửa sản phẩm";
}

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-edit"></i> @ViewData["Title"]</h3>
            </div>
            <div class="card-body">
                <form asp-action="Update" method="post">
                    <input type="hidden" asp-for="Id" />
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Name" class="form-label">Tên sản phẩm <span class="text-danger">*</span></label>
                                <input asp-for="Name" class="form-control" placeholder="Nhập tên sản phẩm" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Price" class="form-label">Giá <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input asp-for="Price" class="form-control" placeholder="0" type="number" step="0.01" min="0" />
                                    <span class="input-group-text">VNĐ</span>
                                </div>
                                <span asp-validation-for="Price" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="CategoryId" class="form-label">Danh mục <span class="text-danger">*</span></label>
                                <select asp-for="CategoryId" asp-items="ViewBag.Categories" class="form-select">
                                    <option value="">-- Chọn danh mục --</option>
                                </select>
                                <span asp-validation-for="CategoryId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ImageUrl" class="form-label">URL hình ảnh</label>
                                <input asp-for="ImageUrl" class="form-control" placeholder="https://example.com/image.jpg" />
                                <span asp-validation-for="ImageUrl" class="text-danger"></span>
                                <small class="form-text text-muted">Nhập đường dẫn URL của hình ảnh sản phẩm</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-4">
                        <label asp-for="Description" class="form-label">Mô tả sản phẩm</label>
                        <textarea asp-for="Description" class="form-control" rows="4" placeholder="Nhập mô tả chi tiết về sản phẩm..."></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save"></i> Cập nhật sản phẩm
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}