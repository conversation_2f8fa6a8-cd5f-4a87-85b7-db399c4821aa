﻿using System.ComponentModel.DataAnnotations;

namespace THLTW_B2.Models
{
    public class Product
    {
        public int Id { get; set; }
        [Required, StringLength(100)]
        public string Name { get; set; } = string.Empty;
        [Range(0.01, 10000.00)]
        public decimal Price { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? ImageUrl { get; set; }
        public int CategoryId { get; set; }
        public List<ProductImage>? Images { get; set; }
        public Category? Category { get; set; }
    }
}
