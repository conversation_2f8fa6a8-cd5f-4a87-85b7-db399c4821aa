using Microsoft.AspNetCore.Identity;
using THLTW_B2.DataAccess;
using THLTW_B2.Models;

namespace THLTW_B2.Services
{
    public class SeedDataService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SeedDataService> _logger;

        public SeedDataService(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ApplicationDbContext context,
            ILogger<SeedDataService> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _context = context;
            _logger = logger;
        }

        public async Task SeedAsync()
        {
            try
            {
                // Tạo roles
                await CreateRoleIfNotExists("Admin");
                await CreateRoleIfNotExists("User");

                // Tạo admin mặc định
                await CreateDefaultAdmin();

                // Tạo categories mẫu
                await CreateSampleCategories();

                _logger.LogInformation("Seed data completed successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while seeding data.");
            }
        }

        private async Task CreateRoleIfNotExists(string roleName)
        {
            if (!await _roleManager.RoleExistsAsync(roleName))
            {
                var role = new IdentityRole(roleName);
                var result = await _roleManager.CreateAsync(role);
                
                if (result.Succeeded)
                {
                    _logger.LogInformation("Role {RoleName} created successfully.", roleName);
                }
                else
                {
                    _logger.LogError("Failed to create role {RoleName}: {Errors}", 
                        roleName, string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }
        }

        private async Task CreateDefaultAdmin()
        {
            const string adminEmail = "<EMAIL>";
            const string adminPassword = "Admin123!";

            var adminUser = await _userManager.FindByEmailAsync(adminEmail);
            
            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FullName = "Quản trị viên hệ thống",
                    EmailConfirmed = true,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                var result = await _userManager.CreateAsync(adminUser, adminPassword);

                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(adminUser, "Admin");
                    await _userManager.AddToRoleAsync(adminUser, "User");
                    
                    _logger.LogInformation("Default admin user created successfully with email: {Email}", adminEmail);
                    _logger.LogInformation("Default admin password: {Password}", adminPassword);
                }
                else
                {
                    _logger.LogError("Failed to create default admin user: {Errors}", 
                        string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }
            else
            {
                // Đảm bảo admin có đủ quyền
                if (!await _userManager.IsInRoleAsync(adminUser, "Admin"))
                {
                    await _userManager.AddToRoleAsync(adminUser, "Admin");
                    _logger.LogInformation("Added Admin role to existing user: {Email}", adminEmail);
                }
                
                if (!await _userManager.IsInRoleAsync(adminUser, "User"))
                {
                    await _userManager.AddToRoleAsync(adminUser, "User");
                    _logger.LogInformation("Added User role to existing user: {Email}", adminEmail);
                }
            }
        }

        private async Task CreateSampleCategories()
        {
            if (!_context.Categories.Any())
            {
                var categories = new List<Category>
                {
                    new Category { Name = "Điện thoại" },
                    new Category { Name = "Laptop" },
                    new Category { Name = "Tablet" },
                    new Category { Name = "Phụ kiện" },
                    new Category { Name = "Đồng hồ thông minh" },
                    new Category { Name = "Tai nghe" }
                };

                _context.Categories.AddRange(categories);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Sample categories created successfully.");
            }
        }
    }
}
